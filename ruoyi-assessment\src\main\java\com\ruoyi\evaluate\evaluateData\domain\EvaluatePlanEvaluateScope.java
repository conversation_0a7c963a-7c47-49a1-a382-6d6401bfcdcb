package com.ruoyi.evaluate.evaluateData.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 评估计划-评估范围对象 dsa_evaluate_plan_evaluate_scope
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_evaluate_plan_evaluate_scope")
public class EvaluatePlanEvaluateScope extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** id */
    @TableId(value = "id" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 单位ID */
    @Excel(name = "单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 计划ID */
    @Excel(name = "计划ID")
    @TableField(value = "plan_id")
    private Long planId;

    /** 承载系统ID */
    @Excel(name = "承载系统ID")
    @TableField(value = "system_ids")
    private String systemIds;

    /** 数据项ID */
    @Excel(name = "数据项ID")
    @TableField(value = "data_item_ids")
    private String dataItemIds;

    /** 数据处理活动ID */
    @Excel(name = "数据处理活动ID")
    @TableField(value = "activity_ids")
    private String activityIds;


    /** $column.columnComment */
    @Excel(name = "${comment}" , readConverterExp = "$column.readConverterExp()")
    @TableField(value = "status")
    private String status;






}