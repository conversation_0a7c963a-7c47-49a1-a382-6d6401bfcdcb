<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateData.mapper.EvaluatePlanEvaluateScopeMapper">
    
    <resultMap type="EvaluatePlanEvaluateScope" id="EvaluatePlanEvaluateScopeResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="systemIds"    column="system_ids"    />
        <result property="dataItemIds"    column="data_item_ids"    />
        <result property="activityIds"    column="activity_ids"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEvaluatePlanEvaluateScopeVo">
        select id, org_id, plan_id, system_ids, data_item_ids, activity_ids, remark, status, create_by, create_time, update_by, update_time, del_flag from dsa_evaluate_plan_evaluate_scope
    </sql>

    <select id="selectEvaluatePlanEvaluateScopeList" parameterType="EvaluatePlanEvaluateScope" resultMap="EvaluatePlanEvaluateScopeResult">
        <include refid="selectEvaluatePlanEvaluateScopeVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="systemIds != null  and systemIds != ''"> and system_ids = #{systemIds}</if>
            <if test="dataItemIds != null  and dataItemIds != ''"> and data_item_ids = #{dataItemIds}</if>
            <if test="activityIds != null  and activityIds != ''"> and activity_ids = #{activityIds}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectEvaluatePlanEvaluateScopeById" parameterType="Long" resultMap="EvaluatePlanEvaluateScopeResult">
        <include refid="selectEvaluatePlanEvaluateScopeVo"/>
        where id = #{id}
    </select>

    <insert id="insertEvaluatePlanEvaluateScope" parameterType="EvaluatePlanEvaluateScope">
        insert into dsa_evaluate_plan_evaluate_scope
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orgId != null">org_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="systemIds != null">system_ids,</if>
            <if test="dataItemIds != null">data_item_ids,</if>
            <if test="activityIds != null">activity_ids,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="systemIds != null">#{systemIds},</if>
            <if test="dataItemIds != null">#{dataItemIds},</if>
            <if test="activityIds != null">#{activityIds},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEvaluatePlanEvaluateScope" parameterType="EvaluatePlanEvaluateScope">
        update dsa_evaluate_plan_evaluate_scope
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="systemIds != null">system_ids = #{systemIds},</if>
            <if test="dataItemIds != null">data_item_ids = #{dataItemIds},</if>
            <if test="activityIds != null">activity_ids = #{activityIds},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluatePlanEvaluateScopeById" parameterType="Long">
        delete from dsa_evaluate_plan_evaluate_scope where id = #{id}
    </delete>

    <delete id="deleteEvaluatePlanEvaluateScopeByIds" parameterType="String">
        delete from dsa_evaluate_plan_evaluate_scope where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>