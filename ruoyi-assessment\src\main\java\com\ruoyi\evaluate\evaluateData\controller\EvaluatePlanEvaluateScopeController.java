package com.ruoyi.evaluate.evaluateData.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateData.domain.EvaluatePlanEvaluateScope;
import com.ruoyi.evaluate.evaluateData.service.IEvaluatePlanEvaluateScopeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 评估计划-评估范围Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/evaluateData/evaluateScope")
@Api(value = "评估计划-评估范围控制器", tags = {"评估计划-评估范围管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluatePlanEvaluateScopeController extends BaseController
{
    private final IEvaluatePlanEvaluateScopeService evaluatePlanEvaluateScopeService;

    /**
     * 查询评估计划-评估范围列表
     */
    @ApiOperation("查询评估计划-评估范围列表")
    @PreAuthorize("@ss.hasPermi('evaluateData:evaluateScope:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluatePlanEvaluateScope evaluatePlanEvaluateScope) {
        startPage();
        List<EvaluatePlanEvaluateScope> list = evaluatePlanEvaluateScopeService.list(new QueryWrapper<EvaluatePlanEvaluateScope>(evaluatePlanEvaluateScope));
        return getDataTable(list);
    }

    /**
     * 获取评估计划-评估范围详细信息
     */
    @ApiOperation("获取评估计划-评估范围详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateData:evaluateScope:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluatePlanEvaluateScopeService.getById(id));
    }

    /**
     * 新增评估计划-评估范围
     */
    @ApiOperation("新增评估计划-评估范围")
    @PreAuthorize("@ss.hasPermi('evaluateData:evaluateScope:add')")
    @Log(title = "评估计划-评估范围", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluatePlanEvaluateScope evaluatePlanEvaluateScope) {
        return toAjax(evaluatePlanEvaluateScopeService.save(evaluatePlanEvaluateScope));
    }

    /**
     * 修改评估计划-评估范围
     */
    @ApiOperation("修改评估计划-评估范围")
    @PreAuthorize("@ss.hasPermi('evaluateData:evaluateScope:edit')")
    @Log(title = "评估计划-评估范围", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluatePlanEvaluateScope evaluatePlanEvaluateScope) {
        return toAjax(evaluatePlanEvaluateScopeService.updateById(evaluatePlanEvaluateScope));
    }

    /**
     * 删除评估计划-评估范围
     */
    @ApiOperation("删除评估计划-评估范围")
    @PreAuthorize("@ss.hasPermi('evaluateData:evaluateScope:remove')")
    @Log(title = "评估计划-评估范围", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluatePlanEvaluateScopeService.removeByIds(Arrays.asList(ids)));
    }
}