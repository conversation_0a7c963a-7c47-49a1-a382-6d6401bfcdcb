package com.ruoyi.evaluate.evaluateData.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateData.mapper.EvaluatePlanEvaluateScopeMapper;
import com.ruoyi.evaluate.evaluateData.domain.EvaluatePlanEvaluateScope;
import com.ruoyi.evaluate.evaluateData.service.IEvaluatePlanEvaluateScopeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 评估计划-评估范围Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class EvaluatePlanEvaluateScopeServiceImpl extends ServiceImpl<EvaluatePlanEvaluateScopeMapper, EvaluatePlanEvaluateScope> implements IEvaluatePlanEvaluateScopeService {

}
