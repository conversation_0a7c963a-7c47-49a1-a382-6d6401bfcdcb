package com.ruoyi.evaluate.evaluatePlan.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.dto.SaveStepRequest;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanStepDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanTaskService;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanStepDataService;
import com.ruoyi.common.core.page.TableDataInfo;

import javax.validation.Valid;

/**
 * 评估计划任务Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/evaluatePlan/task")
@Api(value = "评估计划任务控制器", tags = {"评估计划任务管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluatePlanTaskController extends BaseController {
    private final IEvaluatePlanTaskService evaluatePlanTaskService;

    private final IEvaluatePlanStepDataService evaluatePlanStepDataService;

    /**
     * 查询评估计划任务列表
     */
    @ApiOperation("查询评估计划任务列表")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluatePlanTask evaluatePlanTask) {
        startPage();
        List<EvaluatePlanTask> list = evaluatePlanTaskService.list(new QueryWrapper<EvaluatePlanTask>(evaluatePlanTask));
        return getDataTable(list);
    }

    /**
     * 获取评估计划任务详细信息
     */
    @ApiOperation("获取评估计划任务详细信息")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(evaluatePlanTaskService.getById(id));
    }

    /**
     * 新增评估计划任务
     */
    @ApiOperation("新增评估计划任务")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:add')")
    @Log(title = "评估计划任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluatePlanTaskDto evaluatePlanTaskDto) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("id", evaluatePlanTaskService.createPlanTask(evaluatePlanTaskDto));
            return AjaxResult.success("成功", result);
        } catch (Exception e) {
            return handleException(e, "创建评估计划任务失败");
        }
    }

    /**
     * 修改评估计划任务
     */
    @ApiOperation("修改评估计划任务")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:edit')")
    @Log(title = "评估计划任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluatePlanTask evaluatePlanTask) {
        return toAjax(evaluatePlanTaskService.updateById(evaluatePlanTask));
    }

    /**
     * 删除评估计划任务
     */
    @ApiOperation("删除评估计划任务")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:remove')")
    @Log(title = "评估计划任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(evaluatePlanTaskService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 保存当前步骤数据
     */
    @ApiOperation("保存当前步骤数据")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:edit')")
    @Log(title = "评估计划任务", businessType = BusinessType.UPDATE)
    @PostMapping("/saveStepData")
    public AjaxResult saveCurrentStepData(@Validated @RequestBody SaveStepRequest saveStepRequest) {
        try {
            Boolean result = evaluatePlanStepDataService.saveCurrentStepData(saveStepRequest);
            if (result) {
                return AjaxResult.success("保存步骤数据成功");
            } else {
                return AjaxResult.error("保存步骤数据失败");
            }
        } catch (Exception e) {
            return handleException(e, "保存步骤数据失败");
        }
    }

    /**
     * 暂存评估计划任务数据
     * 根据评估计划ID和当前步骤自动生成暂存键，永久保存直到进入下一步
     */
    @ApiOperation("暂存评估计划任务数据")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:draft')")
    @Log(title = "暂存评估计划任务", businessType = BusinessType.INSERT)
    @PostMapping("/saveDraftStepData")
    public AjaxResult saveDraft(@Valid @RequestBody SaveStepRequest request) {
        try {
            Long userId = SecurityUtils.getUserId();
            Map<String, Object> result = evaluatePlanStepDataService.saveDraftData(userId, request);

            if ((Boolean) result.get("success")) {
                return AjaxResult.success("暂存成功", result);
            } else {
                return AjaxResult.error("暂存失败");
            }
        } catch (Exception e) {
            return handleException(e, "暂存评估计划任务数据失败");
        }
    }

    /**
     * 获取评估计划指定步骤的数据
     */
    @ApiOperation("获取评估计划指定步骤的数据")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:query')")
    @GetMapping("/getStepData/{planId}/{stepCode}")
    public AjaxResult getStepData(
            @ApiParam(value = "评估计划ID", required = true) @PathVariable("planId") Long planId,
            @ApiParam(value = "步骤编码", required = true) @PathVariable("stepCode") String stepCode) {
        try {
            StepDataResponse stepData = evaluatePlanStepDataService.getStepData(planId, stepCode);
            return AjaxResult.success("获取步骤数据成功", stepData);
        } catch (Exception e) {
            return handleException(e, "获取步骤数据失败");
        }
    }

    /**
     * 获取评估计划指定步骤的暂存数据
     */
    @ApiOperation("获取评估计划指定步骤的暂存数据")
    @PreAuthorize("@ss.hasPermi('evaluatePlan:task:query')")
    @GetMapping("/getDraftStepData/{planId}/{stepCode}")
    public AjaxResult getDraftStepData(
            @ApiParam(value = "评估计划ID", required = true) @PathVariable("planId") Long planId,
            @ApiParam(value = "步骤编码", required = true) @PathVariable("stepCode") String stepCode) {
        try {
            // 获取评估计划任务
            EvaluatePlanTask planTask = evaluatePlanTaskService.getById(planId);
            if (planTask == null) {
                return AjaxResult.error("评估计划任务不存在");
            }

            // 获取流程实例ID（可以为null，由处理器内部处理）
            StepDataResponse stepData = evaluatePlanStepDataService.getDraftData(planTask, stepCode);
            return AjaxResult.success("获取暂存数据成功", stepData);
        } catch (Exception e) {
            return handleException(e, "获取暂存数据失败");
        }
    }
}