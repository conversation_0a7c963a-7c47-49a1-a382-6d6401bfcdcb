package com.ruoyi.evaluate.evaluatePlan.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.SaveStepRequest;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import com.ruoyi.process.enums.ProcessStepStatusEnum;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanStepDataService;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanTaskService;
import com.ruoyi.evaluate.evaluatePlan.service.processor.IStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.service.processor.StepDataProcessorFactory;
import com.ruoyi.process.domain.ProcessInstance;
import com.ruoyi.process.domain.ProcessStepInstance;
import com.ruoyi.process.domain.ProcessStepDefinition;
import com.ruoyi.process.service.IProcessFlowService;
import com.ruoyi.process.service.IProcessStepInstanceService;
import com.ruoyi.process.service.IProcessStepDefinitionService;

/**
 * 评估计划步骤数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluatePlanStepDataServiceImpl implements IEvaluatePlanStepDataService {

    private final IEvaluatePlanTaskService evaluatePlanTaskService;
    private final IProcessFlowService processFlowService;
    private final IProcessStepInstanceService processStepInstanceService;
    private final IProcessStepDefinitionService processStepDefinitionService;
    private final StepDataProcessorFactory stepDataProcessorFactory;

    @Override
    public StepDataResponse getStepData(Long planId, String stepCode) {
        try {
            // 获取或创建流程实例ID
            Long processInstanceId = getProcessInstanceId(planId);

            log.info("开始获取步骤数据，计划ID: {}, 步骤编码: {}, 流程实例ID: {}", planId, stepCode, processInstanceId);

            // 参数校验
            validateGetStepDataParams(planId, stepCode);

            // 获取评估计划任务和步骤处理器（共用逻辑）
            EvaluatePlanTask planTask = getEvaluatePlanTask(planId);
            IStepDataProcessor processor = getStepDataProcessor(planId, stepCode);

            // 处理步骤数据
            StepDataResponse stepData = processor.getStepData(planTask, stepCode, processInstanceId);

            // 添加基础信息
            addBaseStepInfo(stepData, planTask, stepCode, processInstanceId);

            log.info("获取步骤数据成功，计划ID: {}, 步骤编码: {}", planId, stepCode);
            return stepData;

        } catch (Exception e) {
            log.error("获取步骤数据失败，计划ID: {}, 步骤编码: {}", planId, stepCode, e);
            throw new ServiceException("获取步骤数据失败: " + e.getMessage());
        }
    }

    /**
     * 参数校验
     */
    private void validateGetStepDataParams(Long planId, String stepCode) {
        if (planId == null) {
            throw new ServiceException("评估计划ID不能为空");
        }
        if (!StringUtils.hasText(stepCode)) {
            throw new ServiceException("步骤编码不能为空");
        }
    }

    /**
     * 暂存数据参数校验（包含用户ID校验）
     */
    private void validateGetDraftDataParams(Long userId, Long planId, String stepCode) {
        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }
        // 复用基础参数校验
        validateGetStepDataParams(planId, stepCode);
    }

    /**
     * 获取评估计划任务
     */
    private EvaluatePlanTask getEvaluatePlanTask(Long planId) {
        EvaluatePlanTask planTask = evaluatePlanTaskService.getById(planId);
        if (planTask == null) {
            throw new ServiceException("评估计划任务不存在，ID: " + planId);
        }
        return planTask;
    }

    /**
     * 获取步骤数据处理器（共用逻辑）
     */
    private IStepDataProcessor getStepDataProcessor(Long planId, String stepCode) {
        // 获取评估计划任务
        EvaluatePlanTask planTask = getEvaluatePlanTask(planId);

        // 校验评估类型
        String evaluateType = planTask.getEvaluateType();
        if (!StringUtils.hasText(evaluateType)) {
            log.warn("评估计划任务的评估类型为空，任务ID: {}", planId);
            throw new ServiceException("评估计划任务的评估类型不能为空");
        }

        // 获取步骤处理器
        return stepDataProcessorFactory.getProcessor(evaluateType, stepCode);
    }

    /**
     * 获取或创建流程实例ID
     */
    private Long getProcessInstanceId(Long planId) {
        // 根据业务ID查找流程实例
        ProcessInstance processInstance = processFlowService.getProcessInstanceByBusinessId(planId);
        if (processInstance == null) {
            throw new ServiceException("未找到对应的流程实例，计划ID: " + planId);
        }

        return processInstance.getId();
    }

    /**
     * 添加基础步骤信息
     */
    private void addBaseStepInfo(StepDataResponse stepData, EvaluatePlanTask planTask,
                                 String stepCode, Long processInstanceId) {
        // stepData.addStepData("planId", planTask.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCurrentStepData(SaveStepRequest draftSaveRequest) {
        try {
            // 1. 参数校验
            validateSaveStepDataRequest(draftSaveRequest);

            // 2. 获取评估计划任务
            EvaluatePlanTask evaluatePlanTask = getEvaluatePlanTask(draftSaveRequest.getPlanId());

            // 3. 获取流程实例
            ProcessInstance processInstance = getProcessInstanceByPlanId(draftSaveRequest.getPlanId());

            // 4. 检查上一步是否完成（新增）
            validatePreviousStepCompleted(processInstance.getId(), draftSaveRequest.getStepCode());

            // 4. 获取评估类型
            String evaluateType = evaluatePlanTask.getEvaluateType();
            if (StringUtils.isEmpty(evaluateType)) {
                log.warn("评估计划任务的评估类型为空，任务ID: {}", draftSaveRequest.getPlanId());
                throw new ServiceException("评估计划任务的评估类型不能为空");
            }

            // 5. 通过工厂获取对应的处理器
            IStepDataProcessor processor = stepDataProcessorFactory.getProcessor(evaluateType, draftSaveRequest.getStepCode());

            // 6. 调用处理器保存步骤数据
            boolean saveResult = processor.saveStepData(
                    evaluatePlanTask,
                    draftSaveRequest.getStepCode(),
                    draftSaveRequest.getStepData(),
                    processInstance.getId()
            );

            if (!saveResult) {
                throw new ServiceException("处理器保存步骤数据失败");
            }

            // 7. 保存成功后，将当前步骤设置为完成状态
            boolean stepCompleted = completeCurrentStep(processInstance.getId(), draftSaveRequest.getStepCode());
            if (!stepCompleted) {
                log.warn("设置步骤完成状态失败，但数据保存成功，任务ID: {}, 步骤: {}",
                        draftSaveRequest.getPlanId(), draftSaveRequest.getStepCode());
            }

            log.info("保存步骤数据成功，任务ID: {}, 步骤: {}, 评估类型: {}, 步骤完成状态: {}",
                    draftSaveRequest.getPlanId(), draftSaveRequest.getStepCode(), evaluateType, stepCompleted);

            return true;

        } catch (ServiceException e) {
            log.error("保存步骤数据失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("保存步骤数据异常，任务ID: {}, 步骤: {}, 异常: {}",
                    draftSaveRequest.getPlanId(), draftSaveRequest.getStepCode(), e.getMessage(), e);
            throw new ServiceException("保存步骤数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean draftCurrentStepData(SaveStepRequest draftSaveRequest) {
        try {
            // 1. 基本参数校验（不进行严格校验）
            validateDraftStepDataRequest(draftSaveRequest);

            // 2. 获取评估计划任务（允许不存在，用于暂存）
            EvaluatePlanTask evaluatePlanTask = evaluatePlanTaskService.getById(draftSaveRequest.getPlanId());

            // 3. 获取流程实例（允许不存在，用于暂存）
            ProcessInstance processInstance = getProcessInstanceByPlanId(draftSaveRequest.getPlanId());

            // 4. 获取评估类型
            String evaluateType = evaluatePlanTask.getEvaluateType();

            // 5. 通过工厂获取对应的处理器（如果可能的话）
            try {
                if (!StringUtils.isEmpty(evaluateType)) {
                    IStepDataProcessor processor = stepDataProcessorFactory.getProcessor(evaluateType, draftSaveRequest.getStepCode());

                    // 6. 调用处理器保存步骤数据
                    Long processInstanceId = processInstance.getId();
                    boolean saveResult = processor.saveStepData(
                            evaluatePlanTask,
                            draftSaveRequest.getStepCode(),
                            draftSaveRequest.getStepData(),
                            processInstanceId
                    );

                    if (!saveResult) {
                        log.warn("处理器暂存步骤数据返回失败，但暂存操作继续");
                    }
                }
            } catch (Exception e) {
                log.warn("暂存数据时处理器操作失败，任务ID: {}, 步骤: {}, 异常: {}，暂存操作继续",
                        draftSaveRequest.getPlanId(), draftSaveRequest.getStepCode(), e.getMessage());
                // 对于暂存操作，处理器失败也允许继续
            }

            log.info("暂存步骤数据成功，任务ID: {}, 步骤: {}, 评估类型: {}",
                    draftSaveRequest.getPlanId(), draftSaveRequest.getStepCode(), evaluateType);

            return true;

        } catch (ServiceException e) {
            log.error("暂存步骤数据失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("暂存步骤数据异常，任务ID: {}, 步骤: {}, 异常: {}",
                    draftSaveRequest.getPlanId(), draftSaveRequest.getStepCode(), e.getMessage(), e);
            throw new ServiceException("暂存步骤数据失败: " + e.getMessage());
        }
    }

    /**
     * 校验保存步骤数据请求参数
     */
    private void validateSaveStepDataRequest(SaveStepRequest draftSaveRequest) {
        if (draftSaveRequest == null) {
            throw new ServiceException("保存步骤数据请求参数不能为空");
        }
        if (!draftSaveRequest.isValid()) {
            throw new ServiceException("保存步骤数据请求参数无效");
        }
    }

    /**
     * 校验暂存步骤数据请求参数（宽松校验）
     */
    private void validateDraftStepDataRequest(SaveStepRequest draftSaveRequest) {
        if (draftSaveRequest == null) {
            throw new ServiceException("暂存步骤数据请求参数不能为空");
        }
        // 对于暂存，只校验基本的必要参数
        if (draftSaveRequest.getPlanId() == null) {
            throw new ServiceException("评估计划ID不能为空");
        }
        if (StringUtils.isEmpty(draftSaveRequest.getStepCode())) {
            throw new ServiceException("步骤名称不能为空");
        }
    }


    /**
     * 根据计划ID获取流程实例
     */
    private ProcessInstance getProcessInstanceByPlanId(Long planId) {
        ProcessInstance processInstance = processFlowService.getProcessInstanceByBusinessId(planId);
        if (processInstance == null) {
            throw new ServiceException("未找到评估计划任务对应的流程实例，任务ID: " + planId);
        }
        return processInstance;
    }

    /**
     * 完成当前步骤
     *
     * @param processInstanceId 流程实例ID
     * @param stepCode          步骤编码
     * @return 是否成功
     */
    private boolean completeCurrentStep(Long processInstanceId, String stepCode) {
        try {
            // 1. 获取流程实例的所有步骤实例
            List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstanceId);
            if (stepInstances == null || stepInstances.isEmpty()) {
                log.warn("未找到流程实例的步骤实例，流程实例ID: {}", processInstanceId);
                return false;
            }

            // 2. 查找当前步骤实例 - 根据stepCode正确匹配
            ProcessStepInstance currentStepInstance = null;
            for (ProcessStepInstance stepInstance : stepInstances) {
                // 通过stepDefinitionId查找对应的步骤定义，然后比较stepCode
                if (stepInstance.getStepDefinitionId() != null) {
                    ProcessStepDefinition stepDefinition = processStepDefinitionService.getById(stepInstance.getStepDefinitionId());
                    if (stepDefinition != null && stepCode.equals(stepDefinition.getStepCode())) {
                        currentStepInstance = stepInstance;
                        break;
                    }
                }
            }

            if (currentStepInstance == null) {
                log.warn("未找到对应的步骤实例，步骤编码: {}, 流程实例ID: {}", stepCode, processInstanceId);
                throw new ServiceException("未找到对应的步骤实例，步骤编码: " + stepCode + ", 流程实例ID: " + processInstanceId);
            }

            // 3. 检查步骤是否已经完成
            if (ProcessStepStatusEnum.FINISHED.getCode().equals(currentStepInstance.getStatus())) {
                log.info("步骤已经完成，无需重复标记，步骤实例ID: {}", currentStepInstance.getId());
                return true;
            }

            // 4. 标记步骤为完成
            boolean updateResult = processFlowService.updateStepInstanceStatus(
                    currentStepInstance.getId(),
                    ProcessStepStatusEnum.FINISHED.getCode(),
                    SecurityUtils.getUsername(),
                    "步骤数据保存完成，自动标记步骤完成"
            );

            if (updateResult) {
                log.info("成功标记步骤完成，步骤实例ID: {}, 步骤编码: {}", currentStepInstance.getId(), stepCode);

                // 5. 标记下一个步骤的开始时间
                markNextStepStartTime(processInstanceId, SecurityUtils.getUsername());
            } else {
                log.warn("标记步骤完成失败，步骤实例ID: {}, 步骤编码: {}", currentStepInstance.getId(), stepCode);
            }

            return updateResult;

        } catch (Exception e) {
            log.error("完成当前步骤时发生异常，流程实例ID: {}, 步骤编码: {}", processInstanceId, stepCode, e);
            return false;
        }
    }

    /**
     * 标记下一个步骤的开始时间
     *
     * @param processInstanceId 流程实例ID
     * @param operator          操作人
     */
    @Override
    public void markNextStepStartTime(Long processInstanceId, String operator) {
        try {
            // 1. 获取下一个待执行的步骤
            ProcessStepInstance nextStepInstance = processStepInstanceService.getOne(
                    new LambdaQueryWrapper<ProcessStepInstance>()
                            .eq(ProcessStepInstance::getProcessInstanceId, processInstanceId)
                            .eq(ProcessStepInstance::getStatus, ProcessStepStatusEnum.PENDING.getCode())
                            .orderByAsc(ProcessStepInstance::getId)
                            .last("LIMIT 1"));

            if (nextStepInstance == null) {
                log.info("没有找到下一个待执行的步骤，流程实例ID: {}", processInstanceId);
                return;
            }

            // 2. 只更新下一个步骤状态为执行中，并设置开始时间
            nextStepInstance.setStatus(ProcessStepStatusEnum.RUNNING.getCode());
            nextStepInstance.setStartTime(new Date());
            nextStepInstance.setUpdateTime(new Date());

            boolean updateResult = processStepInstanceService.updateById(nextStepInstance);

            if (updateResult) {
                log.info("成功标记下一个步骤开始，步骤实例ID: {}, 步骤名称: {}",
                        nextStepInstance.getId(), nextStepInstance.getStepName());
            } else {
                log.warn("标记下一个步骤开始失败，步骤实例ID: {}, 步骤名称: {}",
                        nextStepInstance.getId(), nextStepInstance.getStepName());
            }

        } catch (Exception e) {
            log.error("标记下一个步骤开始时间时发生异常，流程实例ID: {}", processInstanceId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 验证上一步是否完成
     *
     * @param processInstanceId 流程实例ID
     * @param currentStepCode   当前步骤编码
     * @throws ServiceException 如果上一步未完成
     */
    private void validatePreviousStepCompleted(Long processInstanceId, String currentStepCode) {
        try {
            // 1. 获取流程实例的所有步骤实例
            List<ProcessStepInstance> stepInstances = processFlowService.getStepInstancesByProcessId(processInstanceId);

            if (stepInstances == null || stepInstances.isEmpty()) {
                log.warn("未找到步骤实例，流程实例ID: {}", processInstanceId);
                return; // 没有步骤实例，不需要检查
            }

            // 2. 查找当前步骤实例
            ProcessStepInstance currentStepInstance = null;
            for (ProcessStepInstance stepInstance : stepInstances) {
                if (stepInstance.getStepDefinitionId() != null) {
                    ProcessStepDefinition stepDefinition = processStepDefinitionService.getById(stepInstance.getStepDefinitionId());
                    if (stepDefinition != null && currentStepCode.equals(stepDefinition.getStepCode())) {
                        currentStepInstance = stepInstance;
                        break;
                    }
                }
            }

            if (currentStepInstance == null) {
                log.warn("未找到当前步骤实例，步骤编码: {}, 流程实例ID: {}", currentStepCode, processInstanceId);
                return; // 找不到当前步骤，不需要检查
            }

            // 3. 获取当前步骤的步骤定义，确定步骤顺序
            ProcessStepDefinition currentStepDefinition = processStepDefinitionService.getById(currentStepInstance.getStepDefinitionId());
            if (currentStepDefinition == null) {
                log.warn("未找到当前步骤定义，步骤定义ID: {}", currentStepInstance.getStepDefinitionId());
                return;
            }

            Long currentStepOrder = currentStepDefinition.getStepOrder();
            if (currentStepOrder == null || currentStepOrder <= 1) {
                // 第一个步骤或步骤顺序异常，不需要检查上一步
                log.debug("当前步骤是第一个步骤或步骤顺序异常，无需检查上一步，步骤编码: {}, 步骤顺序: {}",
                        currentStepCode, currentStepOrder);
                return;
            }

            // 4. 查找上一步骤实例
            ProcessStepInstance previousStepInstance = null;
            for (ProcessStepInstance stepInstance : stepInstances) {
                if (stepInstance.getStepDefinitionId() != null) {
                    ProcessStepDefinition stepDefinition = processStepDefinitionService.getById(stepInstance.getStepDefinitionId());
                    if (stepDefinition != null && stepDefinition.getStepOrder() != null
                            && stepDefinition.getStepOrder().equals(currentStepOrder - 1)) {
                        previousStepInstance = stepInstance;
                        break;
                    }
                }
            }

            if (previousStepInstance == null) {
                log.warn("未找到上一步骤实例，当前步骤编码: {}, 当前步骤顺序: {}", currentStepCode, currentStepOrder);
                return; // 找不到上一步，可能是流程配置问题，不阻止操作
            }

            // 5. 检查上一步是否完成
            if (!ProcessStepStatusEnum.FINISHED.getCode().equals(previousStepInstance.getStatus())) {
                ProcessStepDefinition previousStepDefinition = processStepDefinitionService.getById(previousStepInstance.getStepDefinitionId());
                String previousStepName = previousStepDefinition != null ? previousStepDefinition.getStepName() : "未知步骤";
                String previousStepCode = previousStepDefinition != null ? previousStepDefinition.getStepCode() : "unknown";

                log.warn("上一步骤未完成，无法保存当前步骤数据。上一步骤: {} ({}), 状态: {}, 当前步骤: {} ({})",
                        previousStepName, previousStepCode, previousStepInstance.getStatus(),
                        currentStepDefinition.getStepName(), currentStepCode);

                throw new ServiceException(String.format("上一步骤 \"%s\" 尚未完成，请先完成上一步骤后再保存当前步骤数据", previousStepName));
            }

            log.debug("上一步骤已完成，可以保存当前步骤数据。当前步骤: {} ({})",
                    currentStepDefinition.getStepName(), currentStepCode);

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("检查上一步完成状态时发生异常，流程实例ID: {}, 当前步骤编码: {}, 异常: {}",
                    processInstanceId, currentStepCode, e.getMessage(), e);
            // 异常情况下不阻止操作，记录日志即可
        }
    }

    /**
     * 获取评估计划步骤暂存数据
     * 优化后复用 getStepData 方法的共用逻辑
     * 参数与getStepData方法保持一致
     */
    @Override
    public StepDataResponse getDraftData(EvaluatePlanTask planTask, String stepCode) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();

        try {
            Long processInstanceId = getProcessInstanceId(planTask.getId());

            log.info("开始获取暂存数据，用户ID: {}, 计划ID: {}, 步骤编码: {}, 流程实例ID: {}",
                    userId, planTask.getId(), stepCode, processInstanceId);

            // 参数校验（包含用户ID校验）
            validateGetDraftDataParams(userId, planTask.getId(), stepCode);

            // 获取步骤处理器（共用逻辑）
            IStepDataProcessor processor = getStepDataProcessor(planTask.getId(), stepCode);

            // 处理暂存数据获取
            StepDataResponse draftData = processor.getDraftData(planTask, stepCode, processInstanceId);
            draftData.getStepInstance().setDraftDataType(processor.getDraftDataType());

            log.info("获取暂存数据成功，用户ID: {}, 计划ID: {}, 步骤编码: {}", userId, planTask.getId(), stepCode);
            return draftData;

        } catch (Exception e) {
            log.error("获取暂存数据失败，用户ID: {}, 计划ID: {}, 步骤编码: {}", userId, planTask.getId(), stepCode, e);
            throw new ServiceException("获取暂存数据失败: " + e.getMessage());
        }
    }

    /**
     * 暂存评估计划步骤数据
     * 优化后复用共用逻辑
     */
    @Override
    public Map<String, Object> saveDraftData(Long userId, SaveStepRequest request) {
        try {
            // 参数校验
            validateSaveStepRequest(request);

            // 获取步骤处理器（共用逻辑）
            IStepDataProcessor processor = getStepDataProcessor(request.getPlanId(), request.getStepCode());

            log.debug("保存暂存数据，用户ID: {}, 步骤: {}, 数据类型: {}", userId, request.getStepCode(), processor.getDraftDataType());

            // 委托给处理器处理暂存数据保存
            return processor.saveDraftData(userId, request.getPlanId(), request.getStepCode(), request.getStepData());

        } catch (Exception e) {
            log.error("暂存数据异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("暂存数据失败：" + e.getMessage());
        }
    }


    /**
     * 校验保存步骤请求参数
     */
    private void validateSaveStepRequest(SaveStepRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException("请求参数无效：" + request.getDescription());
        }
    }
}
